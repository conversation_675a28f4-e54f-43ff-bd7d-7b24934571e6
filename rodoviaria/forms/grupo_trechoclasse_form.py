from datetime import datetime
from typing import NamedTuple, Optional

from pydantic import BaseModel

from rodoviaria.models import Grupo, TrechoClasse


class CidadeInfos(BaseModel):
    id: int
    timezone: str
    name: str
    city_code_ibge: Optional[int]


class TrechoClasseInternoInfos(BaseModel):
    trechoclasse_id: int
    localembarque_origem_id: int
    cidade_origem: CidadeInfos
    localembarque_destino_id: int
    cidade_destino: CidadeInfos
    trecho_datetime_ida: datetime
    grupo_id: int
    grupo_datetime_ida: datetime
    grupoclasse_id: int
    tipo_assento: str
    extra: dict | None = None


class GrupoTrechoClasseInfo(NamedTuple):
    grupo: Grupo
    trecho_classe: TrechoClasse
    last_update: datetime


class GrupoInfo(NamedTuple):
    grupo_id: int
    datetime_ida: datetime
