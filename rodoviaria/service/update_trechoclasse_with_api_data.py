import json

from commons.dateutils import to_tz
from rodoviaria.api.executors.middlewares.token_bucket_middleware import token_bucket_middleware
from rodoviaria.api.forms import ServicoForm
from rodoviaria.models.core import LocalEmbarque, TrechoClasse, TrechoClasseError
from rodoviaria.service.exceptions import RodoviariaTrechoclasseNotFoundException
from rodoviaria.service.servicos_proximos_svc import motivo_servico_nao_encontrado


def update_trechoclasse_with_api_data(rodoviaria_company, api, trechoclasse: TrechoClasse):
    apidata = get_api_data(
        rodoviaria_company,
        api,
        trechoclasse.trechoclasse_internal_id,
        trechoclasse.datetime_ida,
        trechoclasse.origem.cidade.timezone,
        trechoclasse.origem,
        trechoclasse.destino,
        trechoclasse.grupo_classe.tipo_assento_internal,
    )
    trecho_classe, _ = TrechoClasse.objects.update_or_create(
        trechoclasse_internal_id=trechoclasse.trechoclasse_internal_id,
        defaults={
            "external_id": apidata.external_id,
            "external_datetime_ida": apidata.external_datetime_ida,
            "preco_rodoviaria": apidata.preco,
            "external_id_tipo_veiculo": apidata.tipo_veiculo,
            "desconto": apidata.desconto,
            "external_id_desconto": apidata.id_desconto,
            "provider_data": json.dumps(apidata.provider_data),
        },
    )
    return trecho_classe


def get_api_data(
    rodoviaria_company,
    api,
    trechoclasse_internal_id,
    datetime_ida,
    origem_timezone,
    embarque_origem: LocalEmbarque,
    embarque_destino: LocalEmbarque,
    tipo_assento,
    extra: dict | None = None,
) -> ServicoForm:
    with token_bucket_middleware.suppress_not_enough_tokens_error():
        match_params = {
            "datetime_ida": datetime_ida,
            "timezone": origem_timezone,
            "tipo_assento": tipo_assento,
        }
        if extra:
            match_params["extra"] = extra

        servicos = api.buscar_corridas(
            {
                "origem": embarque_origem.id_external,
                "origem_internal_id": embarque_origem.local_embarque_internal_id,
                "destino": embarque_destino.id_external,
                "destino_internal_id": embarque_destino.local_embarque_internal_id,
                "data": to_tz(datetime_ida, origem_timezone).strftime("%Y-%m-%d"),
            },
            match_params,
        )
    if not servicos.found:
        trecho_classe_error = _save_servicos_proximos(
            rodoviaria_company,
            servicos.servicos,
            trechoclasse_internal_id,
            datetime_ida,
            embarque_origem.id,
            embarque_destino.id,
            tipo_assento,
            origem_timezone,
        )
        raise RodoviariaTrechoclasseNotFoundException(
            trechoclasse_id=trechoclasse_internal_id,
            motivo=trecho_classe_error.motivo_fechamento,
        )
    else:
        return servicos.servicos[0]


def _save_servicos_proximos(
    company,
    servicos_proximos,
    trechoclasse_internal_id,
    datetime_ida,
    embarque_origem_id,
    embarque_destino_id,
    tipo_assento,
    origem_timezone,
):
    trecho_classe_error = (
        TrechoClasseError.objects.filter(trechoclasse_internal_id=trechoclasse_internal_id)
        .order_by("-updated_at")
        .first()
    )
    if not trecho_classe_error:
        trecho_classe_error = TrechoClasseError()
    trecho_classe_error.company = company
    trecho_classe_error.trechoclasse_internal_id = trechoclasse_internal_id
    trecho_classe_error.tipo_assento = tipo_assento.lower()
    trecho_classe_error.datetime_ida = datetime_ida
    trecho_classe_error.origem_id = embarque_origem_id
    trecho_classe_error.destino_id = embarque_destino_id
    trecho_classe_error.servicos_proximos = [servico.dict() for servico in servicos_proximos]
    motivo, servicos_proximos_parseados = motivo_servico_nao_encontrado(
        company, datetime_ida, origem_timezone, tipo_assento.lower(), trecho_classe_error.servicos_proximos
    )
    trecho_classe_error.motivo = motivo
    trecho_classe_error.servicos_proximos_parseados = servicos_proximos_parseados
    trecho_classe_error.save()
    return trecho_classe_error
